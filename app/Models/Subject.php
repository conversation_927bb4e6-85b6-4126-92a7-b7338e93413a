<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class Subject extends Model
{
    protected $fillable = [
        'uuid',
        'name',
        'description',
        'user_id',
        'parent_id',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($subject) {
            if (empty($subject->uuid)) {
                $subject->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Relação com o usuário que criou o assunto
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relação com o assunto pai (para estrutura de árvore)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Subject::class, 'parent_id');
    }

    /**
     * Relação com os assuntos filhos (para estrutura de árvore)
     */
    public function children(): HasMany
    {
        return $this->hasMany(Subject::class, 'parent_id');
    }

    /**
     * Relação ManyToMany com prompts
     */
    public function prompts(): BelongsToMany
    {
        return $this->belongsToMany(Prompt::class, 'prompt_subject');
    }

    /**
     * Usar UUID como chave de rota
     */
    public function getRouteKeyName()
    {
        return 'uuid';
    }

    /**
     * Scope para buscar apenas assuntos raiz (sem pai)
     */
    public function scopeRoots($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope para buscar assuntos de um usuário específico
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Get the depth level of this subject in the tree
     */
    public function getDepthLevel(): int
    {
        $level = 0;
        $current = $this;

        while ($current->parent_id) {
            $level++;
            $current = $current->parent;

            // Prevent infinite loops
            if ($level > 10) {
                break;
            }
        }

        return $level;
    }

    /**
     * Get the full path from root to this subject
     */
    public function getFullPath(): string
    {
        $path = [];
        $current = $this;

        while ($current) {
            array_unshift($path, $current->name);
            $current = $current->parent;

            // Prevent infinite loops
            if (count($path) > 10) {
                break;
            }
        }

        return implode(' > ', $path);
    }

    /**
     * Get total count of all descendants (children, grandchildren, etc.)
     */
    public function getTotalDescendantsCount(): int
    {
        $count = $this->children->count();

        foreach ($this->children as $child) {
            $count += $child->getTotalDescendantsCount();
        }

        return $count;
    }

    /**
     * Get all descendants recursively
     */
    public function getAllDescendants()
    {
        $descendants = collect();

        foreach ($this->children as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($child->getAllDescendants());
        }

        return $descendants;
    }

    /**
     * Check if this subject can be a parent of the given subject
     */
    public function canBeParentOf(Subject $subject): bool
    {
        // Can't be parent of itself
        if ($this->id === $subject->id) {
            return false;
        }

        // Can't be parent if it would create a circular reference
        $ancestors = $this->getAllAncestors();
        return !$ancestors->contains('id', $subject->id);
    }

    /**
     * Get all ancestors recursively
     */
    public function getAllAncestors()
    {
        $ancestors = collect();
        $current = $this->parent;

        while ($current) {
            $ancestors->push($current);
            $current = $current->parent;

            // Prevent infinite loops
            if ($ancestors->count() > 10) {
                break;
            }
        }

        return $ancestors;
    }
}

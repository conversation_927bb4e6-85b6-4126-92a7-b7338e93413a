<?php

namespace App\Console\Commands;

use App\Models\Response;
use App\Services\ContentProcessingService;
use Illuminate\Console\Command;

class ProcessExistingResponses extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'responses:process-existing {--dry-run : Show what would be processed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process existing responses to separate reasoning content from main content';

    /**
     * Execute the console command.
     */
    public function handle(ContentProcessingService $contentProcessor)
    {
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('Running in dry-run mode. No changes will be made.');
        }

        // Find responses that have reasoning tags in content but null reasoning field
        $responses = Response::whereNull('reasoning')
            ->where('content', 'LIKE', '%<think>%')
            ->get();

        if ($responses->isEmpty()) {
            $this->info('No responses found that need processing.');
            return;
        }

        $this->info("Found {$responses->count()} responses to process.");

        $bar = $this->output->createProgressBar($responses->count());
        $bar->start();

        $processed = 0;
        $errors = 0;

        foreach ($responses as $response) {
            try {
                $processedContent = $contentProcessor->processResponseContent($response->content);

                if ($dryRun) {
                    $this->newLine();
                    $this->info("Response ID {$response->id}:");
                    $this->line("  Has reasoning: " . (!empty($processedContent['reasoning']) ? 'Yes' : 'No'));
                    if (!empty($processedContent['reasoning'])) {
                        $this->line("  Reasoning length: " . strlen($processedContent['reasoning']) . " chars");
                        $this->line("  Content length: " . strlen($processedContent['content']) . " chars");
                    }
                } else {
                    $response->update([
                        'content' => $processedContent['content'],
                        'reasoning' => $processedContent['reasoning'],
                    ]);
                    $processed++;
                }
            } catch (\Exception $e) {
                $errors++;
                $this->newLine();
                $this->error("Error processing response ID {$response->id}: " . $e->getMessage());
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();

        if ($dryRun) {
            $this->info('Dry run completed. Use without --dry-run to actually process the responses.');
        } else {
            $this->info("Processing completed!");
            $this->info("Successfully processed: {$processed}");
            if ($errors > 0) {
                $this->warn("Errors encountered: {$errors}");
            }
        }
    }
}

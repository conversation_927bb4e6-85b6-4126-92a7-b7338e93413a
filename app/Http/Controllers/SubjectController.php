<?php

namespace App\Http\Controllers;

use App\Models\Subject;
use App\Http\Requests\StoreSubjectRequest;
use App\Http\Requests\UpdateSubjectRequest;
use App\Http\Resources\SubjectResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class SubjectController extends Controller
{
    /**
     * Display a listing of subjects for the authenticated user
     */
    public function index(Request $request): JsonResponse
    {
        $query = Subject::with(['user', 'parent', 'children'])
            ->withCount(['prompts', 'children'])
            ->forUser(Auth::id());

        // Filtrar apenas assuntos raiz se solicitado
        if ($request->boolean('roots_only')) {
            $query->roots();
        }

        // Filtrar por assunto pai se especificado
        if ($request->has('parent_id')) {
            $query->where('parent_id', $request->parent_id);
        }

        // Busca por nome ou descrição
        if ($request->has('search') && $request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Ordenação
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $allowedSorts = ['name', 'created_at', 'updated_at', 'prompts_count', 'children_count'];

        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('name', 'asc');
        }

        // Paginação se solicitada
        if ($request->boolean('paginate')) {
            $perPage = $request->get('per_page', 15);
            $subjects = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => SubjectResource::collection($subjects->items()),
                'meta' => [
                    'current_page' => $subjects->currentPage(),
                    'last_page' => $subjects->lastPage(),
                    'per_page' => $subjects->perPage(),
                    'total' => $subjects->total(),
                    'from' => $subjects->firstItem(),
                    'to' => $subjects->lastItem(),
                ],
                'message' => 'Assuntos recuperados com sucesso'
            ]);
        }

        $subjects = $query->get();

        return response()->json([
            'data' => SubjectResource::collection($subjects),
            'message' => 'Assuntos recuperados com sucesso'
        ]);
    }

    /**
     * Store a newly created subject
     */
    public function store(StoreSubjectRequest $request): JsonResponse
    {
        $validated = $request->validated();

        // Converter UUID do parent para ID se fornecido
        $parentId = null;
        if (!empty($validated['parent_id'])) {
            $parent = Subject::where('uuid', $validated['parent_id'])
                ->orWhere('id', $validated['parent_id'])
                ->where('user_id', Auth::id())
                ->first();

            if ($parent) {
                $parentId = $parent->id;
            }
        }

        $subject = Subject::create([
            'name' => $validated['name'],
            'description' => $validated['description'] ?? null,
            'user_id' => Auth::id(),
            'parent_id' => $parentId
        ]);

        $subject->load(['user', 'parent', 'children']);

        return response()->json([
            'data' => new SubjectResource($subject),
            'message' => 'Assunto criado com sucesso'
        ], 201);
    }

    /**
     * Display the specified subject
     */
    public function show(Subject $subject): JsonResponse
    {
        // Verificar se o assunto pertence ao usuário autenticado
        if ($subject->user_id !== Auth::id()) {
            return response()->json([
                'message' => 'Assunto não encontrado'
            ], 404);
        }

        $subject->load(['user', 'parent', 'children', 'prompts']);

        return response()->json([
            'data' => new SubjectResource($subject),
            'message' => 'Assunto recuperado com sucesso'
        ]);
    }

    /**
     * Update the specified subject
     */
    public function update(UpdateSubjectRequest $request, Subject $subject): JsonResponse
    {
        $validated = $request->validated();

        // Converter UUID do parent para ID se fornecido
        if (isset($validated['parent_id']) && !empty($validated['parent_id'])) {
            $parent = Subject::where('uuid', $validated['parent_id'])
                ->orWhere('id', $validated['parent_id'])
                ->where('user_id', Auth::id())
                ->first();

            if ($parent) {
                $validated['parent_id'] = $parent->id;
            }
        }

        $subject->update($validated);
        $subject->load(['user', 'parent', 'children']);

        return response()->json([
            'data' => new SubjectResource($subject),
            'message' => 'Assunto atualizado com sucesso'
        ]);
    }

    /**
     * Remove the specified subject
     */
    public function destroy(Subject $subject): JsonResponse
    {
        // Verificar se o assunto pertence ao usuário autenticado
        if ($subject->user_id !== Auth::id()) {
            return response()->json([
                'message' => 'Assunto não encontrado'
            ], 404);
        }

        // Verificar se há assuntos filhos
        if ($subject->children()->count() > 0) {
            return response()->json([
                'message' => 'Não é possível excluir um assunto que possui sub-assuntos'
            ], 422);
        }

        $subject->delete();

        return response()->json([
            'message' => 'Assunto excluído com sucesso'
        ]);
    }

    /**
     * Get all root subjects (subjects without parent) for the authenticated user
     */
    public function roots(): JsonResponse
    {
        $subjects = Subject::with(['user', 'children'])
            ->forUser(Auth::id())
            ->roots()
            ->orderBy('name')
            ->get();

        return response()->json([
            'data' => SubjectResource::collection($subjects),
            'message' => 'Assuntos raiz recuperados com sucesso'
        ]);
    }

    /**
     * Get all children of a specific subject
     */
    public function children(Subject $subject): JsonResponse
    {
        // Verificar se o assunto pertence ao usuário autenticado
        if ($subject->user_id !== Auth::id()) {
            return response()->json([
                'message' => 'Assunto não encontrado'
            ], 404);
        }

        $children = $subject->children()
            ->with(['user', 'children'])
            ->orderBy('name')
            ->get();

        return response()->json([
            'data' => SubjectResource::collection($children),
            'message' => 'Sub-assuntos recuperados com sucesso'
        ]);
    }

    /**
     * Get statistics for subjects
     */
    public function statistics(): JsonResponse
    {
        $userId = Auth::id();

        $stats = [
            'total_subjects' => Subject::forUser($userId)->count(),
            'root_subjects' => Subject::forUser($userId)->roots()->count(),
            'subjects_with_prompts' => Subject::forUser($userId)
                ->whereHas('prompts')
                ->count(),
            'total_prompt_associations' => Subject::forUser($userId)
                ->withCount('prompts')
                ->get()
                ->sum('prompts_count'),
            'most_used_subjects' => Subject::forUser($userId)
                ->withCount('prompts')
                ->get()
                ->filter(function ($subject) {
                    return $subject->prompts_count > 0;
                })
                ->sortByDesc('prompts_count')
                ->take(5)
                ->map(function ($subject) {
                    return [
                        'uuid' => $subject->uuid,
                        'name' => $subject->name,
                        'prompts_count' => $subject->prompts_count,
                    ];
                })
                ->values(),
            'recent_subjects' => Subject::forUser($userId)
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($subject) {
                    return [
                        'uuid' => $subject->uuid,
                        'name' => $subject->name,
                        'created_at' => $subject->created_at->toISOString(),
                    ];
                }),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Estatísticas recuperadas com sucesso'
        ]);
    }

    /**
     * Get tree structure of all subjects for the authenticated user
     */
    public function tree(): JsonResponse
    {
        $rootSubjects = Subject::with(['children' => function ($query) {
            $query->with(['children' => function ($subQuery) {
                $subQuery->with(['children'])->withCount(['prompts', 'children']);
            }])->withCount(['prompts', 'children']);
        }])
        ->withCount(['prompts', 'children'])
        ->forUser(Auth::id())
        ->roots()
        ->orderBy('name')
        ->get();

        return response()->json([
            'data' => SubjectResource::collection($rootSubjects),
            'message' => 'Árvore de assuntos recuperada com sucesso'
        ]);
    }

    /**
     * Search subjects by name or description
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:2|max:255',
            'include_children' => 'boolean',
            'per_page' => 'integer|min:1|max:100'
        ]);

        $query = Subject::with(['user', 'parent'])
            ->withCount(['prompts', 'children'])
            ->forUser(Auth::id());

        $searchTerm = $request->query;
        $query->where(function ($q) use ($searchTerm) {
            $q->where('name', 'like', "%{$searchTerm}%")
              ->orWhere('description', 'like', "%{$searchTerm}%");
        });

        if ($request->boolean('include_children')) {
            $query->with(['children' => function ($childQuery) {
                $childQuery->withCount(['prompts', 'children']);
            }]);
        }

        $perPage = $request->get('per_page', 15);
        $subjects = $query->orderBy('name')->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => SubjectResource::collection($subjects->items()),
            'meta' => [
                'current_page' => $subjects->currentPage(),
                'last_page' => $subjects->lastPage(),
                'per_page' => $subjects->perPage(),
                'total' => $subjects->total(),
                'from' => $subjects->firstItem(),
                'to' => $subjects->lastItem(),
            ],
            'message' => 'Busca realizada com sucesso'
        ]);
    }

    /**
     * Get subjects that can be used as parents (excluding the subject itself and its descendants)
     */
    public function availableParents(?Subject $subject = null): JsonResponse
    {
        $query = Subject::forUser(Auth::id())
            ->orderBy('name');

        if ($subject) {
            // Verificar se o assunto pertence ao usuário autenticado
            if ($subject->user_id !== Auth::id()) {
                return response()->json([
                    'message' => 'Assunto não encontrado'
                ], 404);
            }

            // Excluir o próprio assunto e seus descendentes
            $excludeIds = $this->getDescendantIds($subject);
            $excludeIds[] = $subject->id;

            $query->whereNotIn('id', $excludeIds);
        }

        $subjects = $query->get();

        return response()->json([
            'data' => SubjectResource::collection($subjects),
            'message' => 'Assuntos disponíveis como pais recuperados com sucesso'
        ]);
    }

    /**
     * Helper method to get all descendant IDs of a subject
     */
    private function getDescendantIds(Subject $subject): array
    {
        $descendantIds = [];

        foreach ($subject->children as $child) {
            $descendantIds[] = $child->id;
            $descendantIds = array_merge($descendantIds, $this->getDescendantIds($child));
        }

        return $descendantIds;
    }
}

<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdatePromptRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Verificar se o usuário está autenticado e se o prompt pertence a ele
        $prompt = $this->route('prompt');
        return Auth::check() && $prompt && $prompt->user_id === Auth::id();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'content' => 'sometimes|required|string|max:10000',
            'metadata' => 'sometimes|json',
            'subject_ids' => 'sometimes|array',
            'subject_ids.*' => 'string|exists:subjects,uuid',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'content.required' => 'O conteúdo do prompt é obrigatório.',
            'content.max' => 'O conteúdo do prompt não pode exceder 10.000 caracteres.',
        ];
    }
}

<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use App\Models\Subject;

class UpdateSubjectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Verificar se o usuário está autenticado e se o assunto pertence a ele
        $subject = $this->route('subject');
        return Auth::check() && $subject && $subject->user_id === Auth::id();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $subject = $this->route('subject');

        return [
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'parent_id' => [
                'nullable',
                'string',
                function (string $attribute, $value, $fail) use ($subject) {
                    if ($value) {
                        // Tentar encontrar por UUID primeiro, depois por ID
                        $parent = Subject::where('uuid', $value)
                            ->orWhere('id', $value)
                            ->where('user_id', Auth::id())
                            ->first();

                        if (!$parent) {
                            $fail('O assunto pai selecionado não existe.');
                            return;
                        }

                        // Verificar se não está tentando ser pai de si mesmo
                        if ($parent->id == $subject->id) {
                            $fail('Um assunto não pode ser pai de si mesmo.');
                            return;
                        }

                        // Verificar se não criaria uma referência circular
                        if ($this->wouldCreateCircularReference($subject, $parent)) {
                            $fail('Esta operação criaria uma referência circular.');
                        }
                    }
                }
            ]
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'O nome do assunto é obrigatório.',
            'name.max' => 'O nome do assunto não pode ter mais de 255 caracteres.',
            'description.max' => 'A descrição não pode ter mais de 1000 caracteres.',
            'parent_id.exists' => 'O assunto pai selecionado não existe.'
        ];
    }

    /**
     * Verificar se a operação criaria uma referência circular
     */
    private function wouldCreateCircularReference(Subject $subject, Subject $newParent): bool
    {
        $current = $newParent;
        $visited = [];

        while ($current) {
            // Se encontrarmos o assunto original na cadeia de pais, há uma referência circular
            if ($current->id === $subject->id) {
                return true;
            }

            // Evitar loops infinitos
            if (in_array($current->id, $visited)) {
                break;
            }

            $visited[] = $current->id;
            $current = $current->parent;
        }

        return false;
    }
}

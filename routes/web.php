<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Prompt Management Routes
Route::middleware(['auth', 'verified'])->prefix('prompts')->group(function () {
    Route::get('/', function () {
        return Inertia::render('prompts/Index');
    })->name('prompts.index');

    Route::get('/create', function () {
        return Inertia::render('prompts/Create');
    })->name('prompts.create');

    Route::get('/{uuid}', function ($uuid) {
        return Inertia::render('prompts/Show', ['uuid' => $uuid]);
    })->name('prompts.show');

    Route::get('/{uuid}/edit', function ($uuid) {
        return Inertia::render('prompts/Edit', ['uuid' => $uuid]);
    })->name('prompts.edit');
});

// Subject Management Routes
Route::middleware(['auth', 'verified'])->prefix('subjects')->group(function () {
    Route::get('/', function () {
        return Inertia::render('subjects/Index');
    })->name('subjects.index');

    Route::get('/create', function () {
        return Inertia::render('subjects/Create');
    })->name('subjects.create');

    Route::get('/{uuid}', function ($uuid) {
        return Inertia::render('subjects/Show', ['uuid' => $uuid]);
    })->name('subjects.show');

    Route::get('/{uuid}/edit', function ($uuid) {
        return Inertia::render('subjects/Edit', ['uuid' => $uuid]);
    })->name('subjects.edit');
});

// LLM Model Management Routes
Route::middleware(['auth', 'verified'])->prefix('llm-models')->group(function () {
    Route::get('/', function () {
        return Inertia::render('llm-models/Index');
    })->name('llm-models.index');

    Route::get('/create', function () {
        return Inertia::render('llm-models/Create');
    })->name('llm-models.create');

    Route::get('/{uuid}', function ($uuid) {
        return Inertia::render('llm-models/Show', ['uuid' => $uuid]);
    })->name('llm-models.show');

    Route::get('/{uuid}/edit', function ($uuid) {
        return Inertia::render('llm-models/Edit', ['uuid' => $uuid]);
    })->name('llm-models.edit');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

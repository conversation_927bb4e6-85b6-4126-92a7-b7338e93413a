<script lang="ts">
    import { Button } from '@/components/ui/button';
    import { Badge } from '@/components/ui/badge';
    import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
    import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
    import { Checkbox } from '@/components/ui/checkbox';
    import { ScrollArea } from '@/components/ui/scroll-area';
    import { Check, ChevronDown, X, Search, Folder } from 'lucide-svelte';
    import type { Subject } from '@/types';

    interface Props {
        subjects: Subject[];
        selectedSubjects?: Subject[];
        onSelectionChange?: (subjects: Subject[]) => void;
        placeholder?: string;
        multiple?: boolean;
        disabled?: boolean;
        showPath?: boolean;
        maxHeight?: string;
    }

    let {
        subjects = [],
        selectedSubjects = [],
        onSelectionChange,
        placeholder = "Select subjects...",
        multiple = true,
        disabled = false,
        showPath = true,
        maxHeight = "300px"
    }: Props = $props();

    let open = $state(false);
    let searchQuery = $state('');
    let flatSubjects = $state<Subject[]>([]);

    // Flatten subjects for easier searching
    $effect(() => {
        const flatten = (items: Subject[], level = 0): Subject[] => {
            let result: Subject[] = [];
            for (const item of items) {
                result.push({ ...item, depth_level: level });
                if (item.children && item.children.length > 0) {
                    result = result.concat(flatten(item.children, level + 1));
                }
            }
            return result;
        };
        flatSubjects = flatten(subjects);
    });

    // Filter subjects based on search query
    let filteredSubjects = $derived(() => {
        if (!searchQuery.trim()) {
            return flatSubjects;
        }
        
        const query = searchQuery.toLowerCase();
        return flatSubjects.filter(subject => 
            subject.name.toLowerCase().includes(query) ||
            (subject.description && subject.description.toLowerCase().includes(query)) ||
            (showPath && subject.full_path && subject.full_path.toLowerCase().includes(query))
        );
    });

    function isSelected(subject: Subject): boolean {
        return selectedSubjects.some(s => s.uuid === subject.uuid);
    }

    function toggleSubject(subject: Subject) {
        if (disabled) return;

        let newSelection: Subject[];
        
        if (multiple) {
            if (isSelected(subject)) {
                newSelection = selectedSubjects.filter(s => s.uuid !== subject.uuid);
            } else {
                newSelection = [...selectedSubjects, subject];
            }
        } else {
            newSelection = isSelected(subject) ? [] : [subject];
            open = false;
        }

        if (onSelectionChange) {
            onSelectionChange(newSelection);
        }
    }

    function removeSubject(subject: Subject) {
        if (disabled) return;
        
        const newSelection = selectedSubjects.filter(s => s.uuid !== subject.uuid);
        if (onSelectionChange) {
            onSelectionChange(newSelection);
        }
    }

    function clearAll() {
        if (disabled) return;
        
        if (onSelectionChange) {
            onSelectionChange([]);
        }
    }

    function getIndentClass(level: number): string {
        return `pl-${Math.min(level * 4, 16)}`;
    }
</script>

<div class="space-y-2">
    <Popover bind:open>
        <PopoverTrigger>
            <Button
                variant="outline"
                role="combobox"
                aria-expanded={open}
                class="w-full justify-between"
                {disabled}
            >
                <div class="flex items-center gap-2 flex-1 min-w-0">
                    {#if selectedSubjects.length === 0}
                        <span class="text-muted-foreground">{placeholder}</span>
                    {:else if selectedSubjects.length === 1}
                        <span class="truncate">{selectedSubjects[0].name}</span>
                    {:else}
                        <span>{selectedSubjects.length} subjects selected</span>
                    {/if}
                </div>
                <ChevronDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
        </PopoverTrigger>
        
        <PopoverContent class="w-full p-0" style="max-height: {maxHeight};">
            <Command>
                <div class="flex items-center border-b px-3">
                    <Search class="mr-2 h-4 w-4 shrink-0 opacity-50" />
                    <CommandInput 
                        placeholder="Search subjects..." 
                        bind:value={searchQuery}
                        class="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                    />
                </div>
                
                <CommandList>
                    <ScrollArea class="h-full">
                        {#if filteredSubjects().length === 0}
                            <CommandEmpty>No subjects found.</CommandEmpty>
                        {:else}
                            <CommandGroup>
                                {#each filteredSubjects() as subject (subject.uuid)}
                                    <CommandItem
                                        value={subject.uuid}
                                        onSelect={() => toggleSubject(subject)}
                                        class="flex items-center gap-2 cursor-pointer"
                                    >
                                        <div class="flex items-center gap-2 flex-1 min-w-0 {getIndentClass(subject.depth_level || 0)}">
                                            {#if multiple}
                                                <Checkbox 
                                                    checked={isSelected(subject)}
                                                    class="data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                                                />
                                            {:else}
                                                <div class="w-4 h-4 flex items-center justify-center">
                                                    {#if isSelected(subject)}
                                                        <Check class="h-4 w-4" />
                                                    {/if}
                                                </div>
                                            {/if}
                                            
                                            {#if subject.has_children}
                                                <Folder class="h-4 w-4 text-muted-foreground flex-shrink-0" />
                                            {:else}
                                                <div class="h-4 w-4 rounded-full bg-muted flex-shrink-0"></div>
                                            {/if}
                                            
                                            <div class="flex-1 min-w-0">
                                                <div class="font-medium truncate">{subject.name}</div>
                                                {#if showPath && subject.full_path && subject.full_path !== subject.name}
                                                    <div class="text-xs text-muted-foreground truncate">{subject.full_path}</div>
                                                {/if}
                                            </div>
                                            
                                            {#if subject.prompts_count > 0}
                                                <Badge variant="secondary" class="text-xs">
                                                    {subject.prompts_count}
                                                </Badge>
                                            {/if}
                                        </div>
                                    </CommandItem>
                                {/each}
                            </CommandGroup>
                        {/if}
                    </ScrollArea>
                </CommandList>
            </Command>
        </PopoverContent>
    </Popover>

    <!-- Selected subjects display -->
    {#if multiple && selectedSubjects.length > 0}
        <div class="flex flex-wrap gap-2">
            {#each selectedSubjects as subject (subject.uuid)}
                <Badge variant="secondary" class="flex items-center gap-1">
                    <span class="truncate max-w-32">{subject.name}</span>
                    <Button
                        variant="ghost"
                        size="sm"
                        class="h-4 w-4 p-0 hover:bg-transparent"
                        onclick={() => removeSubject(subject)}
                        {disabled}
                    >
                        <X class="h-3 w-3" />
                    </Button>
                </Badge>
            {/each}
            
            {#if selectedSubjects.length > 1}
                <Button
                    variant="ghost"
                    size="sm"
                    class="h-6 text-xs"
                    onclick={clearAll}
                    {disabled}
                >
                    Clear all
                </Button>
            {/if}
        </div>
    {/if}
</div>

<script lang="ts">
    import { But<PERSON> } from '@/components/ui/button';
    import { 
        Dialog, 
        DialogContent, 
        DialogDescription, 
        DialogFooter, 
        DialogHeader, 
        DialogTitle 
    } from '@/components/ui/dialog';
    import { Alert, AlertDescription } from '@/components/ui/alert';
    import { Badge } from '@/components/ui/badge';
    import { AlertTriangle, Trash2, X, Folder, FileText } from 'lucide-svelte';
    import type { Subject } from '@/types';

    interface Props {
        open?: boolean;
        subject?: Subject | null;
        onConfirm?: () => void;
        onCancel?: () => void;
        loading?: boolean;
    }

    let {
        open = false,
        subject = null,
        onConfirm,
        onCancel,
        loading = false
    }: Props = $props();

    function handleConfirm() {
        if (loading) return;
        if (onConfirm) {
            onConfirm();
        }
    }

    function handleCancel() {
        if (onCancel) {
            onCancel();
        }
    }

    let hasChildren = $derived(() => (subject?.children_count || 0) > 0);
    let hasPrompts = $derived(() => (subject?.prompts_count || 0) > 0);
    let canDelete = $derived(() => !hasChildren);
</script>

<Dialog {open} onOpenChange={(isOpen) => !isOpen && handleCancel()}>
    <DialogContent class="sm:max-w-md">
        <DialogHeader>
            <DialogTitle class="flex items-center gap-2">
                <AlertTriangle class="h-5 w-5 text-destructive" />
                Delete Subject
            </DialogTitle>
            <DialogDescription>
                This action cannot be undone. Please review the details below.
            </DialogDescription>
        </DialogHeader>

        {#if subject}
            <div class="space-y-4">
                <!-- Subject Info -->
                <div class="p-4 bg-muted rounded-lg">
                    <div class="flex items-start gap-3">
                        <Folder class="h-5 w-5 text-muted-foreground mt-0.5" />
                        <div class="flex-1 min-w-0">
                            <h3 class="font-medium">{subject.name}</h3>
                            {#if subject.description}
                                <p class="text-sm text-muted-foreground mt-1">{subject.description}</p>
                            {/if}
                            {#if subject.full_path && subject.full_path !== subject.name}
                                <p class="text-xs text-muted-foreground mt-2">
                                    Path: {subject.full_path}
                                </p>
                            {/if}
                        </div>
                    </div>
                </div>

                <!-- Impact Analysis -->
                <div class="space-y-3">
                    <h4 class="font-medium text-sm">Impact Analysis:</h4>
                    
                    <div class="grid gap-2">
                        <!-- Children Count -->
                        <div class="flex items-center justify-between p-2 rounded border">
                            <div class="flex items-center gap-2">
                                <Folder class="h-4 w-4 text-muted-foreground" />
                                <span class="text-sm">Child Subjects</span>
                            </div>
                            <Badge variant={hasChildren ? "destructive" : "secondary"}>
                                {subject.children_count || 0}
                            </Badge>
                        </div>

                        <!-- Prompts Count -->
                        <div class="flex items-center justify-between p-2 rounded border">
                            <div class="flex items-center gap-2">
                                <FileText class="h-4 w-4 text-muted-foreground" />
                                <span class="text-sm">Associated Prompts</span>
                            </div>
                            <Badge variant={hasPrompts ? "secondary" : "outline"}>
                                {subject.prompts_count || 0}
                            </Badge>
                        </div>
                    </div>
                </div>

                <!-- Warnings -->
                {#if hasChildren}
                    <Alert variant="destructive">
                        <AlertTriangle class="h-4 w-4" />
                        <AlertDescription>
                            <strong>Cannot delete this subject.</strong> 
                            It has {subject.children_count} child subject{subject.children_count === 1 ? '' : 's'}. 
                            Please delete or move the child subjects first.
                        </AlertDescription>
                    </Alert>
                {:else if hasPrompts}
                    <Alert>
                        <AlertTriangle class="h-4 w-4" />
                        <AlertDescription>
                            This subject is associated with {subject.prompts_count} prompt{subject.prompts_count === 1 ? '' : 's'}. 
                            Deleting it will remove these associations, but the prompts themselves will not be deleted.
                        </AlertDescription>
                    </Alert>
                {:else}
                    <Alert>
                        <AlertTriangle class="h-4 w-4" />
                        <AlertDescription>
                            This subject has no child subjects or associated prompts. It can be safely deleted.
                        </AlertDescription>
                    </Alert>
                {/if}
            </div>
        {/if}

        <DialogFooter class="gap-2">
            <Button
                variant="outline"
                onclick={handleCancel}
                disabled={loading}
                class="flex items-center gap-2"
            >
                <X class="h-4 w-4" />
                Cancel
            </Button>
            
            <Button
                variant="destructive"
                onclick={handleConfirm}
                disabled={!canDelete || loading}
                class="flex items-center gap-2"
            >
                {#if loading}
                    <div class="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                {:else}
                    <Trash2 class="h-4 w-4" />
                {/if}
                Delete Subject
            </Button>
        </DialogFooter>
    </DialogContent>
</Dialog>

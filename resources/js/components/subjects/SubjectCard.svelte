<script lang="ts">
    import { Button } from '@/components/ui/button';
    import { Badge } from '@/components/ui/badge';
    import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
    import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
    import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
    import {
        Folder,
        MoreVertical,
        Edit,
        Trash2,
        Plus,
        Eye,
        FileText,
        Calendar,
        ChevronRight
    } from 'lucide-svelte';
    import type { Subject } from '@/types';

    interface Props {
        subject: Subject;
        onView?: (subject: Subject) => void;
        onEdit?: (subject: Subject) => void;
        onDelete?: (subject: Subject) => void;
        onAddChild?: (subject: Subject) => void;
        showActions?: boolean;
        showPath?: boolean;
        showStats?: boolean;
        compact?: boolean;
    }

    let {
        subject,
        onView,
        onEdit,
        onDelete,
        onAddChild,
        showActions = true,
        showPath = true,
        showStats = true,
        compact = false
    }: Props = $props();

    function handleView() {
        if (onView) {
            onView(subject);
        }
    }

    function handleEdit() {
        if (onEdit) {
            onEdit(subject);
        }
    }

    function handleDelete() {
        if (onDelete) {
            onDelete(subject);
        }
    }

    function handleAddChild() {
        if (onAddChild) {
            onAddChild(subject);
        }
    }

    function formatDate(dateString: string): string {
        return new Date(dateString).toLocaleDateString();
    }

    function getPathSegments(fullPath: string): string[] {
        return fullPath.split(' > ');
    }
</script>

<Card class={`group hover:shadow-md transition-shadow duration-200 ${compact ? 'h-auto' : ''}`}>
    <CardHeader class={`pb-3 ${compact ? 'pb-2' : ''}`}>
        <div class="flex items-start justify-between">
            <div class="flex items-start gap-3 flex-1 min-w-0">
                <!-- Subject Icon -->
                <div class="flex-shrink-0 mt-1">
                    {#if subject.has_children}
                        <Folder class="h-5 w-5 text-primary" />
                    {:else}
                        <div class="h-5 w-5 rounded-full bg-muted border-2 border-muted-foreground/20"></div>
                    {/if}
                </div>

                <!-- Subject Info -->
                <div class="flex-1 min-w-0">
                    <CardTitle
                        class={`text-lg cursor-pointer hover:text-primary transition-colors ${compact ? 'text-base' : ''}`}
                        onclick={handleView}
                    >
                        {subject.name}
                    </CardTitle>
                    
                    {#if subject.description && !compact}
                        <CardDescription class="mt-1 line-clamp-2">
                            {subject.description}
                        </CardDescription>
                    {/if}

                    <!-- Path Display -->
                    {#if showPath && subject.full_path && subject.full_path !== subject.name}
                        <div class="flex items-center gap-1 mt-2 text-xs text-muted-foreground">
                            {#each getPathSegments(subject.full_path) as segment, index}
                                {#if index > 0}
                                    <ChevronRight class="h-3 w-3" />
                                {/if}
                                <span class="truncate max-w-24" class:font-medium={index === getPathSegments(subject.full_path).length - 1}>
                                    {segment}
                                </span>
                            {/each}
                        </div>
                    {/if}
                </div>
            </div>

            <!-- Actions Menu -->
            {#if showActions}
                <DropdownMenu>
                    <DropdownMenuTrigger>
                        <Button
                            variant="ghost"
                            size="sm"
                            class="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                            <MoreVertical class="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuItem onclick={handleView}>
                            <Eye class="mr-2 h-4 w-4" />
                            View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onclick={handleEdit}>
                            <Edit class="mr-2 h-4 w-4" />
                            Edit Subject
                        </DropdownMenuItem>
                        <DropdownMenuItem onclick={handleAddChild}>
                            <Plus class="mr-2 h-4 w-4" />
                            Add Child Subject
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onclick={handleDelete} class="text-destructive">
                            <Trash2 class="mr-2 h-4 w-4" />
                            Delete Subject
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            {/if}
        </div>
    </CardHeader>

    {#if showStats && !compact}
        <CardContent class="pt-0">
            <div class="flex items-center justify-between">
                <!-- Statistics -->
                <div class="flex items-center gap-4">
                    {#if subject.prompts_count > 0}
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Badge variant="secondary" class="flex items-center gap-1">
                                        <FileText class="h-3 w-3" />
                                        {subject.prompts_count}
                                    </Badge>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>{subject.prompts_count} associated prompts</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    {/if}

                    {#if subject.children_count > 0}
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Badge variant="outline" class="flex items-center gap-1">
                                        <Folder class="h-3 w-3" />
                                        {subject.children_count}
                                    </Badge>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>{subject.children_count} child subjects</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    {/if}

                    {#if subject.is_root}
                        <Badge variant="default" class="text-xs">
                            Root
                        </Badge>
                    {/if}
                </div>

                <!-- Creation Date -->
                <div class="flex items-center gap-1 text-xs text-muted-foreground">
                    <Calendar class="h-3 w-3" />
                    <span>{formatDate(subject.created_at)}</span>
                </div>
            </div>

            <!-- Recent Activity -->
            {#if subject.recent_prompt_activity && subject.recent_prompt_activity.length > 0}
                <div class="mt-4 pt-4 border-t">
                    <h4 class="text-sm font-medium mb-2">Recent Activity</h4>
                    <div class="space-y-1">
                        {#each subject.recent_prompt_activity.slice(0, 2) as activity}
                            <div class="text-xs text-muted-foreground truncate">
                                <span class="font-medium">Prompt:</span> {activity.content}
                            </div>
                        {/each}
                        {#if subject.recent_prompt_activity.length > 2}
                            <div class="text-xs text-muted-foreground">
                                +{subject.recent_prompt_activity.length - 2} more...
                            </div>
                        {/if}
                    </div>
                </div>
            {/if}
        </CardContent>
    {/if}
</Card>

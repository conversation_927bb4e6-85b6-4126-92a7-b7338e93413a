<script lang="ts">
    import { Button } from '@/components/ui/button';
    import { Badge } from '@/components/ui/badge';
    import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
    import { ChevronDown, ChevronRight, Plus, Edit, Trash2, FolderOpen, Folder } from 'lucide-svelte';
    import type { Subject } from '@/types';
    import SubjectTree from './SubjectTree.svelte';

    interface Props {
        subjects: Subject[];
        selectedSubject?: Subject | null;
        onSelect?: (subject: Subject) => void;
        onEdit?: (subject: Subject) => void;
        onDelete?: (subject: Subject) => void;
        onAddChild?: (parent: Subject) => void;
        showActions?: boolean;
        showCounts?: boolean;
        expandAll?: boolean;
    }

    let {
        subjects = [],
        selectedSubject = null,
        onSelect,
        onEdit,
        onDelete,
        onAddChild,
        showActions = true,
        showCounts = true,
        expandAll = false
    }: Props = $props();

    let expandedNodes = $state<Set<string>>(new Set());

    // Initialize expanded state
    $effect(() => {
        if (expandAll) {
            const allIds = new Set<string>();
            const collectIds = (items: Subject[]) => {
                items.forEach(item => {
                    if (item.has_children) {
                        allIds.add(item.uuid);
                    }
                    if (item.children) {
                        collectIds(item.children);
                    }
                });
            };
            collectIds(subjects);
            expandedNodes = allIds;
        }
    });

    function toggleExpanded(uuid: string) {
        if (expandedNodes.has(uuid)) {
            expandedNodes.delete(uuid);
        } else {
            expandedNodes.add(uuid);
        }
        expandedNodes = new Set(expandedNodes);
    }

    function handleSelect(subject: Subject) {
        if (onSelect) {
            onSelect(subject);
        }
    }

    function handleEdit(subject: Subject, event: Event) {
        event.stopPropagation();
        if (onEdit) {
            onEdit(subject);
        }
    }

    function handleDelete(subject: Subject, event: Event) {
        event.stopPropagation();
        if (onDelete) {
            onDelete(subject);
        }
    }

    function handleAddChild(subject: Subject, event: Event) {
        event.stopPropagation();
        if (onAddChild) {
            onAddChild(subject);
        }
    }
</script>

<div class="space-y-1">
    {#each subjects as subject (subject.uuid)}
        <div class="border rounded-lg">
            <Collapsible open={expandedNodes.has(subject.uuid)}>
                <div
                    class="flex items-center gap-2 p-3 hover:bg-muted/50 cursor-pointer transition-colors"
                    class:bg-muted={selectedSubject?.uuid === subject.uuid}
                    role="button"
                    tabindex="0"
                    onclick={() => handleSelect(subject)}
                    onkeydown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            handleSelect(subject);
                        }
                    }}
                >
                    {#if subject.has_children}
                        <CollapsibleTrigger>
                            <Button
                                variant="ghost"
                                size="sm"
                                class="h-6 w-6 p-0"
                                onclick={(e) => {
                                    e.stopPropagation();
                                    toggleExpanded(subject.uuid);
                                }}
                            >
                                {#if expandedNodes.has(subject.uuid)}
                                    <ChevronDown class="h-4 w-4" />
                                {:else}
                                    <ChevronRight class="h-4 w-4" />
                                {/if}
                            </Button>
                        </CollapsibleTrigger>
                    {:else}
                        <div class="w-6"></div>
                    {/if}

                    <div class="flex items-center gap-2 flex-1 min-w-0">
                        {#if subject.has_children}
                            {#if expandedNodes.has(subject.uuid)}
                                <FolderOpen class="h-4 w-4 text-muted-foreground flex-shrink-0" />
                            {:else}
                                <Folder class="h-4 w-4 text-muted-foreground flex-shrink-0" />
                            {/if}
                        {:else}
                            <div class="h-4 w-4 rounded-full bg-muted flex-shrink-0"></div>
                        {/if}

                        <div class="flex-1 min-w-0">
                            <div class="font-medium truncate">{subject.name}</div>
                            {#if subject.description}
                                <div class="text-sm text-muted-foreground truncate">{subject.description}</div>
                            {/if}
                        </div>

                        {#if showCounts}
                            <div class="flex items-center gap-1 flex-shrink-0">
                                {#if subject.prompts_count > 0}
                                    <Badge variant="secondary" class="text-xs">
                                        {subject.prompts_count} prompts
                                    </Badge>
                                {/if}
                                {#if subject.children_count > 0}
                                    <Badge variant="outline" class="text-xs">
                                        {subject.children_count} children
                                    </Badge>
                                {/if}
                            </div>
                        {/if}

                        {#if showActions}
                            <div class="flex items-center gap-1 flex-shrink-0">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    class="h-8 w-8 p-0"
                                    onclick={(e) => handleAddChild(subject, e)}
                                >
                                    <Plus class="h-4 w-4" />
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    class="h-8 w-8 p-0"
                                    onclick={(e) => handleEdit(subject, e)}
                                >
                                    <Edit class="h-4 w-4" />
                                </Button>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    class="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                    onclick={(e) => handleDelete(subject, e)}
                                >
                                    <Trash2 class="h-4 w-4" />
                                </Button>
                            </div>
                        {/if}
                    </div>
                </div>

                {#if subject.children && subject.children.length > 0}
                    <CollapsibleContent class="pl-8 pr-3 pb-3">
                        <SubjectTree
                            subjects={subject.children}
                            {selectedSubject}
                            {onSelect}
                            {onEdit}
                            {onDelete}
                            {onAddChild}
                            {showActions}
                            {showCounts}
                            {expandAll}
                        />
                    </CollapsibleContent>
                {/if}
            </Collapsible>
        </div>
    {/each}
</div>

{#if subjects.length === 0}
    <div class="text-center py-8 text-muted-foreground">
        <Folder class="h-12 w-12 mx-auto mb-4 opacity-50" />
        <p>No subjects found</p>
    </div>
{/if}

<script lang="ts">
    import { Button } from '@/components/ui/button';
    import { Input } from '@/components/ui/input';
    import { Label } from '@/components/ui/label';
    import { Textarea } from '@/components/ui/textarea';
    import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
    import { Alert, AlertDescription } from '@/components/ui/alert';
    import { Badge } from '@/components/ui/badge';
    import SubjectSelector from './SubjectSelector.svelte';
    import { AlertCircle, Save, X, Folder } from 'lucide-svelte';
    import type { Subject } from '@/types';

    interface Props {
        subject?: Subject | null;
        availableParents?: Subject[];
        onSubmit?: (data: any) => void;
        onCancel?: () => void;
        loading?: boolean;
        errors?: Record<string, string[]>;
        mode?: 'create' | 'edit';
    }

    let {
        subject = null,
        availableParents = [],
        onSubmit,
        onCancel,
        loading = false,
        errors = {},
        mode = 'create'
    }: Props = $props();

    let formData = $state({
        name: subject?.name || '',
        description: subject?.description || '',
        parent_id: subject?.parent?.uuid || null
    });

    let selectedParent = $state<Subject[]>(
        subject?.parent ? [subject.parent] : []
    );

    // Update form data when subject changes
    $effect(() => {
        if (subject) {
            formData.name = subject.name || '';
            formData.description = subject.description || '';
            formData.parent_id = subject.parent?.uuid || null;
            selectedParent = subject.parent ? [subject.parent] : [];
        }
    });

    function handleParentChange(parents: Subject[]) {
        selectedParent = parents;
        formData.parent_id = parents.length > 0 ? parents[0].uuid : null;
    }

    function handleSubmit() {
        if (loading) return;

        const submitData = {
            name: formData.name.trim(),
            description: formData.description.trim() || null,
            parent_id: formData.parent_id
        };

        if (onSubmit) {
            onSubmit(submitData);
        }
    }

    function handleCancel() {
        if (onCancel) {
            onCancel();
        }
    }

    function getFieldError(field: string): string | null {
        return errors[field]?.[0] || null;
    }

    let isValid = $derived(() => {
        return formData.name.trim().length > 0;
    });
</script>

<Card class="w-full max-w-2xl">
    <CardHeader>
        <CardTitle class="flex items-center gap-2">
            <Folder class="h-5 w-5" />
            {mode === 'create' ? 'Create New Subject' : 'Edit Subject'}
        </CardTitle>
        <CardDescription>
            {mode === 'create' 
                ? 'Create a new subject to organize your prompts' 
                : 'Update the subject information'}
        </CardDescription>
    </CardHeader>
    
    <CardContent class="space-y-6">
        <form onsubmit={(e) => { e.preventDefault(); handleSubmit(); }} class="space-y-4">
            <!-- Subject Name -->
            <div class="space-y-2">
                <Label for="name">Name *</Label>
                <Input
                    id="name"
                    bind:value={formData.name}
                    placeholder="Enter subject name"
                    disabled={loading}
                    class={getFieldError('name') ? 'border-destructive' : ''}
                />
                {#if getFieldError('name')}
                    <Alert variant="destructive">
                        <AlertCircle class="h-4 w-4" />
                        <AlertDescription>{getFieldError('name')}</AlertDescription>
                    </Alert>
                {/if}
            </div>

            <!-- Subject Description -->
            <div class="space-y-2">
                <Label for="description">Description</Label>
                <Textarea
                    id="description"
                    bind:value={formData.description}
                    placeholder="Enter subject description (optional)"
                    disabled={loading}
                    rows={3}
                    class={getFieldError('description') ? 'border-destructive' : ''}
                />
                {#if getFieldError('description')}
                    <Alert variant="destructive">
                        <AlertCircle class="h-4 w-4" />
                        <AlertDescription>{getFieldError('description')}</AlertDescription>
                    </Alert>
                {/if}
            </div>

            <!-- Parent Subject -->
            <div class="space-y-2">
                <Label for="parent">Parent Subject</Label>
                <SubjectSelector
                    subjects={availableParents}
                    selectedSubjects={selectedParent}
                    onSelectionChange={handleParentChange}
                    placeholder="Select parent subject (optional)"
                    multiple={false}
                    disabled={loading}
                    showPath={true}
                />
                {#if getFieldError('parent_id')}
                    <Alert variant="destructive">
                        <AlertCircle class="h-4 w-4" />
                        <AlertDescription>{getFieldError('parent_id')}</AlertDescription>
                    </Alert>
                {/if}
                <p class="text-sm text-muted-foreground">
                    Leave empty to create a root subject, or select a parent to create a nested subject.
                </p>
            </div>

            <!-- Current Path Preview -->
            {#if selectedParent.length > 0}
                <div class="space-y-2">
                    <Label>Subject Path Preview</Label>
                    <div class="p-3 bg-muted rounded-lg">
                        <div class="flex items-center gap-2 text-sm">
                            <Folder class="h-4 w-4 text-muted-foreground" />
                            <span class="text-muted-foreground">
                                {selectedParent[0].full_path || selectedParent[0].name}
                            </span>
                            <span class="text-muted-foreground">></span>
                            <Badge variant="outline">
                                {formData.name || 'New Subject'}
                            </Badge>
                        </div>
                    </div>
                </div>
            {/if}

            <!-- Form Actions -->
            <div class="flex items-center gap-3 pt-4">
                <Button
                    type="submit"
                    disabled={!isValid || loading}
                    class="flex items-center gap-2"
                >
                    {#if loading}
                        <div class="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                    {:else}
                        <Save class="h-4 w-4" />
                    {/if}
                    {mode === 'create' ? 'Create Subject' : 'Update Subject'}
                </Button>
                
                <Button
                    type="button"
                    variant="outline"
                    onclick={handleCancel}
                    disabled={loading}
                    class="flex items-center gap-2"
                >
                    <X class="h-4 w-4" />
                    Cancel
                </Button>
            </div>

            <!-- General Errors -->
            {#if errors.general}
                <Alert variant="destructive">
                    <AlertCircle class="h-4 w-4" />
                    <AlertDescription>{errors.general[0]}</AlertDescription>
                </Alert>
            {/if}
        </form>
    </CardContent>
</Card>

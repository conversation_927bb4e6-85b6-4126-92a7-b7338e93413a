<script lang="ts">
    import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
    import { Badge } from '@/components/ui/badge';
    import { Skeleton } from '@/components/ui/skeleton';

    import { Folder, FileText, TrendingUp, Clock, ChartBar } from 'lucide-svelte';
    import { onMount } from 'svelte';
    import type { SubjectStatistics } from '@/types';

    interface Props {
        statistics?: SubjectStatistics | null;
        loading?: boolean;
    }

    let { statistics = null, loading = false }: Props = $props();

    let mostUsedChart = $state<HTMLCanvasElement>();
    let distributionChart = $state<HTMLCanvasElement>();
    let chartInstances = $state<any[]>([]);

    onMount(() => {
        return () => {
            // Cleanup charts on unmount
            chartInstances.forEach(chart => chart.destroy());
        };
    });

    $effect(() => {
        if (statistics && !loading) {
            createCharts();
        }
    });

    async function createCharts() {
        // Destroy existing charts
        chartInstances.forEach(chart => chart.destroy());
        chartInstances = [];

        if (!statistics) return;

        // Import Chart.js dynamically
        const { Chart, registerables } = await import('chart.js');
        Chart.register(...registerables);

        // Most Used Subjects Chart
        if (mostUsedChart && statistics.most_used_subjects.length > 0) {
            const mostUsedChartInstance = new Chart(mostUsedChart, {
                type: 'doughnut',
                data: {
                    labels: statistics.most_used_subjects.map(s => s.name),
                    datasets: [{
                        data: statistics.most_used_subjects.map(s => s.prompts_count),
                        backgroundColor: [
                            'hsl(var(--primary))',
                            'hsl(var(--secondary))',
                            'hsl(var(--accent))',
                            'hsl(var(--muted))',
                            'hsl(var(--destructive))'
                        ],
                        borderWidth: 2,
                        borderColor: 'hsl(var(--background))'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return `${label}: ${value} prompts (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
            chartInstances.push(mostUsedChartInstance);
        }

        // Subject Distribution Chart
        if (distributionChart) {
            const distributionData = [
                { label: 'Root Subjects', value: statistics.root_subjects, color: 'hsl(var(--primary))' },
                { label: 'Child Subjects', value: statistics.total_subjects - statistics.root_subjects, color: 'hsl(var(--secondary))' },
                { label: 'With Prompts', value: statistics.subjects_with_prompts, color: 'hsl(var(--accent))' },
                { label: 'Without Prompts', value: statistics.total_subjects - statistics.subjects_with_prompts, color: 'hsl(var(--muted))' }
            ];

            const distributionChartInstance = new Chart(distributionChart, {
                type: 'bar',
                data: {
                    labels: distributionData.map(d => d.label),
                    datasets: [{
                        data: distributionData.map(d => d.value),
                        backgroundColor: distributionData.map(d => d.color),
                        borderWidth: 1,
                        borderColor: 'hsl(var(--border))'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.label}: ${context.parsed.y} subjects`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            }
                        }
                    }
                }
            });
            chartInstances.push(distributionChartInstance);
        }
    }
</script>

{#if loading}
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {#each Array(4) as _}
            <Card>
                <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                    <Skeleton class="h-4 w-24" />
                    <Skeleton class="h-4 w-4" />
                </CardHeader>
                <CardContent>
                    <Skeleton class="h-8 w-16 mb-2" />
                    <Skeleton class="h-3 w-32" />
                </CardContent>
            </Card>
        {/each}
    </div>
{:else if statistics}
    <div class="space-y-6">
        <!-- Overview Cards -->
        <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
                <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle class="text-sm font-medium">Total Subjects</CardTitle>
                    <Folder class="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    <div class="text-2xl font-bold">{statistics.total_subjects}</div>
                    <p class="text-xs text-muted-foreground">
                        {statistics.root_subjects} root subjects
                    </p>
                </CardContent>
            </Card>

            <Card>
                <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle class="text-sm font-medium">With Prompts</CardTitle>
                    <FileText class="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    <div class="text-2xl font-bold">{statistics.subjects_with_prompts}</div>
                    <p class="text-xs text-muted-foreground">
                        {statistics.total_subjects > 0 ? Math.round((statistics.subjects_with_prompts / statistics.total_subjects) * 100) : 0}% of all subjects
                    </p>
                </CardContent>
            </Card>

            <Card>
                <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle class="text-sm font-medium">Total Associations</CardTitle>
                    <TrendingUp class="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    <div class="text-2xl font-bold">{statistics.total_prompt_associations}</div>
                    <p class="text-xs text-muted-foreground">
                        Prompt-subject links
                    </p>
                </CardContent>
            </Card>

            <Card>
                <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle class="text-sm font-medium">Recent Activity</CardTitle>
                    <Clock class="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                    <div class="text-2xl font-bold">{statistics.recent_subjects.length}</div>
                    <p class="text-xs text-muted-foreground">
                        New subjects this period
                    </p>
                </CardContent>
            </Card>
        </div>

        <!-- Charts Section -->
        <div class="grid gap-6 md:grid-cols-2">
            <!-- Most Used Subjects Chart -->
            <Card>
                <CardHeader>
                    <CardTitle class="flex items-center gap-2">
                        <ChartBar class="h-5 w-5" />
                        Most Used Subjects
                    </CardTitle>
                    <CardDescription>
                        Subjects with the highest number of associated prompts
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    {#if statistics.most_used_subjects.length > 0}
                        <div class="h-64">
                            <canvas bind:this={mostUsedChart}></canvas>
                        </div>
                    {:else}
                        <div class="h-64 flex items-center justify-center text-muted-foreground">
                            <div class="text-center">
                                <ChartBar class="h-12 w-12 mx-auto mb-4 opacity-50" />
                                <p>No subject usage data available</p>
                            </div>
                        </div>
                    {/if}
                </CardContent>
            </Card>

            <!-- Subject Distribution Chart -->
            <Card>
                <CardHeader>
                    <CardTitle class="flex items-center gap-2">
                        <Folder class="h-5 w-5" />
                        Subject Distribution
                    </CardTitle>
                    <CardDescription>
                        Overview of subject organization and usage
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="h-64">
                        <canvas bind:this={distributionChart}></canvas>
                    </div>
                </CardContent>
            </Card>
        </div>

        <!-- Recent Subjects -->
        {#if statistics.recent_subjects.length > 0}
            <Card>
                <CardHeader>
                    <CardTitle>Recent Subjects</CardTitle>
                    <CardDescription>
                        Recently created subjects
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div class="space-y-2">
                        {#each statistics.recent_subjects as subject}
                            <div class="flex items-center justify-between p-2 rounded-lg border">
                                <div class="flex items-center gap-2">
                                    <Folder class="h-4 w-4 text-muted-foreground" />
                                    <span class="font-medium">{subject.name}</span>
                                </div>
                                <Badge variant="outline" class="text-xs">
                                    {new Date(subject.created_at).toLocaleDateString()}
                                </Badge>
                            </div>
                        {/each}
                    </div>
                </CardContent>
            </Card>
        {/if}
    </div>
{:else}
    <div class="text-center py-8 text-muted-foreground">
        <ChartBar class="h-12 w-12 mx-auto mb-4 opacity-50" />
        <p>No statistics available</p>
    </div>
{/if}

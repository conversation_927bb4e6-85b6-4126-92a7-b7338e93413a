<script lang="ts">
    import AppLayout from '@/layouts/AppLayout.svelte';
    import { Alert, AlertDescription } from '@/components/ui/alert';
    import { Skeleton } from '@/components/ui/skeleton';
    import SubjectForm from '@/components/subjects/SubjectForm.svelte';
    import { AlertCircle, ArrowLeft } from 'lucide-svelte';
    import { Button } from '@/components/ui/button';
    import { api } from '@/lib/api';
    import { router } from '@inertiajs/svelte';
    import type { Subject, UpdateSubjectRequest, BreadcrumbItem } from '@/types';
    import { onMount } from 'svelte';

    // Props
    interface Props {
        uuid: string;
    }
    let { uuid }: Props = $props();

    // State
    let subject: Subject | null = $state(null);
    let availableParents: Subject[] = $state([]);
    let loading = $state(true);
    let submitLoading = $state(false);
    let error = $state<string | null>(null);
    let errors = $state<Record<string, string[]>>({});

    let breadcrumbs: BreadcrumbItem[] = $state([
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Subjects', href: '/subjects' }
    ]);

    onMount(() => {
        loadSubject();
        loadAvailableParents();
    });

    async function loadSubject() {
        try {
            loading = true;
            error = null;
            
            subject = await api.getSubject(uuid);
            
            // Update breadcrumbs
            breadcrumbs = [
                { title: 'Dashboard', href: '/dashboard' },
                { title: 'Subjects', href: '/subjects' },
                { title: subject.name, href: `/subjects/${subject.uuid}` },
                { title: 'Edit', href: `/subjects/${subject.uuid}/edit` }
            ];
        } catch (err) {
            error = err instanceof Error ? err.message : 'Failed to load subject';
            console.error('Error loading subject:', err);
        } finally {
            loading = false;
        }
    }

    async function loadAvailableParents() {
        try {
            // Load available parents excluding this subject and its descendants
            availableParents = await api.getAvailableParents(uuid);
        } catch (err) {
            console.error('Error loading available parents:', err);
        }
    }

    async function handleSubmit(data: UpdateSubjectRequest) {
        if (!subject) return;
        
        try {
            submitLoading = true;
            error = null;
            errors = {};

            const updatedSubject = await api.updateSubject(subject.uuid, data);
            
            // Redirect to the subject's detail page
            router.visit(`/subjects/${updatedSubject.uuid}`, {
                onSuccess: () => {
                    // Success message will be handled by the show page
                }
            });
        } catch (err) {
            submitLoading = false;
            
            if (err instanceof Error) {
                try {
                    // Try to parse validation errors
                    const errorData = JSON.parse(err.message);
                    if (errorData.errors) {
                        errors = errorData.errors;
                    } else {
                        error = errorData.message || 'Failed to update subject';
                    }
                } catch {
                    error = err.message;
                }
            } else {
                error = 'Failed to update subject';
            }
            
            console.error('Error updating subject:', err);
        }
    }

    function handleCancel() {
        if (subject) {
            router.visit(`/subjects/${subject.uuid}`);
        } else {
            router.visit('/subjects');
        }
    }
</script>

<AppLayout {breadcrumbs}>
    <div class="max-w-4xl mx-auto space-y-6">
        <!-- Back Button -->
        <Button variant="outline" onclick={handleCancel}>
            <ArrowLeft class="h-4 w-4 mr-2" />
            {subject ? `Back to ${subject.name}` : 'Back'}
        </Button>

        {#if loading}
            <!-- Loading State -->
            <div class="space-y-6">
                <div>
                    <Skeleton class="h-8 w-64 mb-2" />
                    <Skeleton class="h-4 w-96" />
                </div>
                <div class="flex justify-center">
                    <div class="w-full max-w-2xl space-y-4">
                        <Skeleton class="h-64 w-full" />
                    </div>
                </div>
            </div>
        {:else if error && !subject}
            <!-- Error State -->
            <Alert variant="destructive">
                <AlertCircle class="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        {:else if subject}
            <!-- Header -->
            <div>
                <h1 class="text-3xl font-bold tracking-tight">Edit Subject</h1>
                <p class="text-muted-foreground">
                    Update the subject information and organization
                </p>
            </div>

            <!-- General Error Alert -->
            {#if error}
                <Alert variant="destructive">
                    <AlertCircle class="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            {/if}

            <!-- Current Subject Info -->
            <div class="bg-muted/50 rounded-lg p-4">
                <h3 class="font-semibold mb-2">Current Subject Information</h3>
                <div class="space-y-1 text-sm">
                    <div><strong>Name:</strong> {subject.name}</div>
                    {#if subject.description}
                        <div><strong>Description:</strong> {subject.description}</div>
                    {/if}
                    {#if subject.full_path && subject.full_path !== subject.name}
                        <div><strong>Current Path:</strong> {subject.full_path}</div>
                    {/if}
                    <div><strong>Type:</strong> {subject.is_root ? 'Root Subject' : 'Child Subject'}</div>
                    <div><strong>Children:</strong> {subject.children_count} child subjects</div>
                    <div><strong>Prompts:</strong> {subject.prompts_count} associated prompts</div>
                </div>
            </div>

            <!-- Warning about children -->
            {#if subject.children_count > 0}
                <Alert>
                    <AlertCircle class="h-4 w-4" />
                    <AlertDescription>
                        <strong>Note:</strong> This subject has {subject.children_count} child subject{subject.children_count === 1 ? '' : 's'}. 
                        Changing the parent will move this entire subtree to a new location in the hierarchy.
                    </AlertDescription>
                </Alert>
            {/if}

            <!-- Subject Form -->
            <div class="flex justify-center">
                <SubjectForm
                    {subject}
                    {availableParents}
                    onSubmit={handleSubmit}
                    onCancel={handleCancel}
                    loading={submitLoading}
                    {errors}
                    mode="edit"
                />
            </div>

            <!-- Help Section -->
            <div class="max-w-2xl mx-auto">
                <div class="bg-muted/50 rounded-lg p-6">
                    <h3 class="font-semibold mb-3">Editing Guidelines</h3>
                    <ul class="space-y-2 text-sm text-muted-foreground">
                        <li class="flex items-start gap-2">
                            <span class="text-primary">•</span>
                            <span>Changes to the name and description will be reflected immediately</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-primary">•</span>
                            <span>Changing the parent will reorganize the subject hierarchy</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-primary">•</span>
                            <span>All child subjects will move with this subject if you change its parent</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-primary">•</span>
                            <span>Associated prompts will remain linked to this subject</span>
                        </li>
                        <li class="flex items-start gap-2">
                            <span class="text-primary">•</span>
                            <span>You cannot create circular references (a subject cannot be its own ancestor)</span>
                        </li>
                    </ul>
                </div>
            </div>
        {/if}
    </div>
</AppLayout>

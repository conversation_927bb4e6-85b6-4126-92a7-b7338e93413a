<script lang="ts">
    import AppLayout from '@/layouts/AppLayout.svelte';
    import { Button } from '@/components/ui/button';
    import { Badge } from '@/components/ui/badge';
    import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
    import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
    import { Alert, AlertDescription } from '@/components/ui/alert';
    import { Skeleton } from '@/components/ui/skeleton';
    import SubjectTree from '@/components/subjects/SubjectTree.svelte';
    import DeleteSubjectDialog from '@/components/subjects/DeleteSubjectDialog.svelte';
    import { 
        Edit, 
        Trash2, 
        Plus, 
        Folder, 
        FileText, 
        Calendar, 
        ChevronRight,
        AlertCircle,
        ArrowLeft
    } from 'lucide-svelte';
    import { api } from '@/lib/api';
    import { router } from '@inertiajs/svelte';
    import type { Subject, BreadcrumbItem } from '@/types';
    import { onMount } from 'svelte';

    // Props
    interface Props {
        uuid: string;
    }
    let { uuid }: Props = $props();

    // State
    let subject: Subject | null = $state(null);
    let children: Subject[] = $state([]);
    let loading = $state(true);
    let childrenLoading = $state(false);
    let error = $state<string | null>(null);
    let showDeleteDialog = $state(false);
    let deleteLoading = $state(false);

    let breadcrumbs: BreadcrumbItem[] = $state([
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Subjects', href: '/subjects' }
    ]);

    onMount(() => {
        loadSubject();
    });

    async function loadSubject() {
        try {
            loading = true;
            error = null;
            
            subject = await api.getSubject(uuid);
            
            // Update breadcrumbs
            breadcrumbs = [
                { title: 'Dashboard', href: '/dashboard' },
                { title: 'Subjects', href: '/subjects' },
                { title: subject.name, href: `/subjects/${subject.uuid}` }
            ];
            
            // Load children if any
            if (subject.has_children) {
                loadChildren();
            }
        } catch (err) {
            error = err instanceof Error ? err.message : 'Failed to load subject';
            console.error('Error loading subject:', err);
        } finally {
            loading = false;
        }
    }

    async function loadChildren() {
        if (!subject) return;
        
        try {
            childrenLoading = true;
            children = await api.getSubjectChildren(subject.uuid);
        } catch (err) {
            console.error('Error loading children:', err);
        } finally {
            childrenLoading = false;
        }
    }

    function handleEdit() {
        if (subject) {
            router.visit(`/subjects/${subject.uuid}/edit`);
        }
    }

    function handleDelete() {
        showDeleteDialog = true;
    }

    function handleAddChild() {
        if (subject) {
            router.visit(`/subjects/create?parent=${subject.uuid}`);
        }
    }

    async function confirmDelete() {
        if (!subject) return;
        
        try {
            deleteLoading = true;
            await api.deleteSubject(subject.uuid);
            
            // Redirect to subjects list
            router.visit('/subjects');
        } catch (err) {
            error = err instanceof Error ? err.message : 'Failed to delete subject';
            console.error('Error deleting subject:', err);
        } finally {
            deleteLoading = false;
            showDeleteDialog = false;
        }
    }

    function cancelDelete() {
        showDeleteDialog = false;
    }

    function handleChildSelect(child: Subject) {
        router.visit(`/subjects/${child.uuid}`);
    }

    function handleChildEdit(child: Subject) {
        router.visit(`/subjects/${child.uuid}/edit`);
    }

    function handleChildDelete(child: Subject) {
        // For now, navigate to the child's page where they can delete it
        router.visit(`/subjects/${child.uuid}`);
    }

    function handleChildAddChild(child: Subject) {
        router.visit(`/subjects/create?parent=${child.uuid}`);
    }

    function formatDate(dateString: string): string {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    function getPathSegments(fullPath: string): string[] {
        return fullPath.split(' > ');
    }
</script>

<AppLayout {breadcrumbs}>
    <div class="space-y-6">
        <!-- Back Button -->
        <Button variant="outline" onclick={() => router.visit('/subjects')}>
            <ArrowLeft class="h-4 w-4 mr-2" />
            Back to Subjects
        </Button>

        <!-- Error Alert -->
        {#if error}
            <Alert variant="destructive">
                <AlertCircle class="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        {/if}

        {#if loading}
            <!-- Loading State -->
            <div class="space-y-6">
                <div class="flex items-center justify-between">
                    <div class="space-y-2">
                        <Skeleton class="h-8 w-64" />
                        <Skeleton class="h-4 w-96" />
                    </div>
                    <div class="flex gap-2">
                        <Skeleton class="h-10 w-24" />
                        <Skeleton class="h-10 w-24" />
                        <Skeleton class="h-10 w-24" />
                    </div>
                </div>
                <Skeleton class="h-64 w-full" />
            </div>
        {:else if subject}
            <!-- Header -->
            <div class="flex items-start justify-between">
                <div class="space-y-4">
                    <div>
                        <div class="flex items-center gap-3 mb-2">
                            <Folder class="h-6 w-6 text-primary" />
                            <h1 class="text-3xl font-bold tracking-tight">{subject.name}</h1>
                        </div>
                        
                        {#if subject.description}
                            <p class="text-lg text-muted-foreground">{subject.description}</p>
                        {/if}
                    </div>

                    <!-- Path Display -->
                    {#if subject.full_path && subject.full_path !== subject.name}
                        <div class="flex items-center gap-1 text-sm text-muted-foreground">
                            <span class="font-medium">Path:</span>
                            {#each getPathSegments(subject.full_path) as segment, index}
                                {#if index > 0}
                                    <ChevronRight class="h-3 w-3" />
                                {/if}
                                <span class={index === getPathSegments(subject.full_path).length - 1 ? 'font-medium text-foreground' : ''}>
                                    {segment}
                                </span>
                            {/each}
                        </div>
                    {/if}

                    <!-- Metadata -->
                    <div class="flex items-center gap-4 text-sm text-muted-foreground">
                        <div class="flex items-center gap-1">
                            <Calendar class="h-4 w-4" />
                            <span>Created {formatDate(subject.created_at)}</span>
                        </div>
                        {#if subject.is_root}
                            <Badge variant="default">Root Subject</Badge>
                        {/if}
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex items-center gap-2">
                    <Button variant="outline" onclick={handleAddChild}>
                        <Plus class="h-4 w-4 mr-2" />
                        Add Child
                    </Button>
                    <Button variant="outline" onclick={handleEdit}>
                        <Edit class="h-4 w-4 mr-2" />
                        Edit
                    </Button>
                    <Button variant="destructive" onclick={handleDelete}>
                        <Trash2 class="h-4 w-4 mr-2" />
                        Delete
                    </Button>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="grid gap-4 md:grid-cols-3">
                <Card>
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle class="text-sm font-medium">Associated Prompts</CardTitle>
                        <FileText class="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div class="text-2xl font-bold">{subject.prompts_count}</div>
                        <p class="text-xs text-muted-foreground">
                            Prompts linked to this subject
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle class="text-sm font-medium">Child Subjects</CardTitle>
                        <Folder class="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div class="text-2xl font-bold">{subject.children_count}</div>
                        <p class="text-xs text-muted-foreground">
                            Direct child subjects
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle class="text-sm font-medium">Total Descendants</CardTitle>
                        <Folder class="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div class="text-2xl font-bold">{subject.total_descendants_count || 0}</div>
                        <p class="text-xs text-muted-foreground">
                            All nested subjects
                        </p>
                    </CardContent>
                </Card>
            </div>

            <!-- Content Tabs -->
            <Tabs value="children" class="space-y-4">
                <TabsList>
                    <TabsTrigger value="children">Child Subjects ({subject.children_count})</TabsTrigger>
                    <TabsTrigger value="prompts">Associated Prompts ({subject.prompts_count})</TabsTrigger>
                    <TabsTrigger value="activity">Recent Activity</TabsTrigger>
                </TabsList>

                <!-- Children Tab -->
                <TabsContent value="children" class="space-y-4">
                    {#if subject.children_count === 0}
                        <Card>
                            <CardContent class="flex flex-col items-center justify-center py-12">
                                <Folder class="h-12 w-12 text-muted-foreground mb-4" />
                                <h3 class="text-lg font-semibold mb-2">No child subjects</h3>
                                <p class="text-muted-foreground text-center mb-4">
                                    This subject doesn't have any child subjects yet.
                                </p>
                                <Button onclick={handleAddChild}>
                                    <Plus class="h-4 w-4 mr-2" />
                                    Add Child Subject
                                </Button>
                            </CardContent>
                        </Card>
                    {:else if childrenLoading}
                        <div class="flex items-center justify-center py-8">
                            <div class="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                        </div>
                    {:else}
                        <SubjectTree
                            subjects={children}
                            onSelect={handleChildSelect}
                            onEdit={handleChildEdit}
                            onDelete={handleChildDelete}
                            onAddChild={handleChildAddChild}
                            showActions={true}
                            showCounts={true}
                            expandAll={true}
                        />
                    {/if}
                </TabsContent>

                <!-- Prompts Tab -->
                <TabsContent value="prompts" class="space-y-4">
                    {#if subject.prompts_count === 0}
                        <Card>
                            <CardContent class="flex flex-col items-center justify-center py-12">
                                <FileText class="h-12 w-12 text-muted-foreground mb-4" />
                                <h3 class="text-lg font-semibold mb-2">No associated prompts</h3>
                                <p class="text-muted-foreground text-center mb-4">
                                    No prompts are currently associated with this subject.
                                </p>
                                <Button onclick={() => router.visit('/prompts/create')}>
                                    <Plus class="h-4 w-4 mr-2" />
                                    Create Prompt
                                </Button>
                            </CardContent>
                        </Card>
                    {:else}
                        <div class="space-y-4">
                            {#if subject.prompts}
                                {#each subject.prompts as prompt}
                                    <Card>
                                        <CardHeader>
                                            <div class="flex items-center justify-between">
                                                <CardTitle class="text-base">{prompt.content.substring(0, 100)}...</CardTitle>
                                                <Badge variant={prompt.status === 'completed' ? 'default' : 'secondary'}>
                                                    {prompt.status}
                                                </Badge>
                                            </div>
                                            <CardDescription>
                                                Created {formatDate(prompt.created_at)}
                                            </CardDescription>
                                        </CardHeader>
                                    </Card>
                                {/each}
                            {:else}
                                <p class="text-muted-foreground">Loading prompts...</p>
                            {/if}
                        </div>
                    {/if}
                </TabsContent>

                <!-- Activity Tab -->
                <TabsContent value="activity" class="space-y-4">
                    {#if subject.recent_prompt_activity && subject.recent_prompt_activity.length > 0}
                        <div class="space-y-4">
                            {#each subject.recent_prompt_activity as activity}
                                <Card>
                                    <CardContent class="pt-4">
                                        <div class="flex items-start gap-3">
                                            <FileText class="h-4 w-4 text-muted-foreground mt-1" />
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm">{activity.content}</p>
                                                <p class="text-xs text-muted-foreground mt-1">
                                                    {formatDate(activity.created_at)}
                                                </p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            {/each}
                        </div>
                    {:else}
                        <Card>
                            <CardContent class="flex flex-col items-center justify-center py-12">
                                <Calendar class="h-12 w-12 text-muted-foreground mb-4" />
                                <h3 class="text-lg font-semibold mb-2">No recent activity</h3>
                                <p class="text-muted-foreground text-center">
                                    No recent prompt activity for this subject.
                                </p>
                            </CardContent>
                        </Card>
                    {/if}
                </TabsContent>
            </Tabs>
        {/if}
    </div>

    <!-- Delete Confirmation Dialog -->
    <DeleteSubjectDialog
        open={showDeleteDialog}
        {subject}
        onConfirm={confirmDelete}
        onCancel={cancelDelete}
        loading={deleteLoading}
    />
</AppLayout>

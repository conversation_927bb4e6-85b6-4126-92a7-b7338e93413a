<script lang="ts">
    import AppLayout from '@/layouts/AppLayout.svelte';
    import { Button } from '@/components/ui/button';
    import { Input } from '@/components/ui/input';
    import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select';
    import { Card, CardContent } from '@/components/ui/card';
    import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
    import { Alert, AlertDescription } from '@/components/ui/alert';
    import SubjectTree from '@/components/subjects/SubjectTree.svelte';
    import SubjectCard from '@/components/subjects/SubjectCard.svelte';
    import SubjectStatistics from '@/components/subjects/SubjectStatistics.svelte';
    import DeleteSubjectDialog from '@/components/subjects/DeleteSubjectDialog.svelte';
    import { Plus, Search, RefreshCw, Grid, TreePine, ChartBar, AlertCircle } from 'lucide-svelte';
    import { api } from '@/lib/api';
    import { router } from '@inertiajs/svelte';
    import type { Subject, SubjectFilters, SubjectStatistics as SubjectStatsType, BreadcrumbItem } from '@/types';
    import { onMount } from 'svelte';

    // State
    let subjects: Subject[] = $state([]);
    let statistics: SubjectStatsType | null = $state(null);
    let loading = $state(true);
    let statisticsLoading = $state(true);
    let error = $state<string | null>(null);
    let selectedSubject: Subject | null = $state(null);
    let subjectToDelete: Subject | null = $state(null);
    let deleteLoading = $state(false);
    
    // Filters
    let filters: SubjectFilters = $state({
        search: '',
        sort_by: 'name',
        sort_order: 'asc',
        roots_only: false
    });
    
    let currentView = $state<'tree' | 'grid' | 'statistics'>('tree');

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Subjects', href: '/subjects' }
    ];

    onMount(() => {
        loadSubjects();
        loadStatistics();
    });

    async function loadSubjects() {
        try {
            loading = true;
            error = null;
            
            if (currentView === 'tree') {
                const response = await api.getSubjectTree();
                subjects = response;
            } else {
                const response = await api.getSubjects(filters);
                subjects = Array.isArray(response) ? response : response.data;
            }
        } catch (err) {
            error = err instanceof Error ? err.message : 'Failed to load subjects';
            console.error('Error loading subjects:', err);
        } finally {
            loading = false;
        }
    }

    async function loadStatistics() {
        try {
            statisticsLoading = true;
            statistics = await api.getSubjectStatistics();
        } catch (err) {
            console.error('Error loading statistics:', err);
        } finally {
            statisticsLoading = false;
        }
    }

    function handleSearch() {
        loadSubjects();
    }

    function handleSortChange(value: string | string[]) {
        const valueStr = Array.isArray(value) ? value[0] : value;
        const [sortBy, sortOrder] = valueStr.split('-');
        filters.sort_by = sortBy;
        filters.sort_order = sortOrder as 'asc' | 'desc';
        loadSubjects();
    }

    function handleViewChange(view: string) {
        currentView = view as 'tree' | 'grid' | 'statistics';
        if (view !== 'statistics') {
            loadSubjects();
        }
    }

    function handleCreateSubject() {
        router.visit('/subjects/create');
    }

    function handleSubjectSelect(subject: Subject) {
        selectedSubject = subject;
        router.visit(`/subjects/${subject.uuid}`);
    }

    function handleSubjectEdit(subject: Subject) {
        router.visit(`/subjects/${subject.uuid}/edit`);
    }

    function handleSubjectDelete(subject: Subject) {
        subjectToDelete = subject;
    }

    function handleAddChild(parent: Subject) {
        router.visit(`/subjects/create?parent=${parent.uuid}`);
    }

    async function confirmDelete() {
        if (!subjectToDelete) return;
        
        try {
            deleteLoading = true;
            await api.deleteSubject(subjectToDelete.uuid);
            
            // Refresh the subjects list
            await loadSubjects();
            await loadStatistics();
            
            subjectToDelete = null;
        } catch (err) {
            error = err instanceof Error ? err.message : 'Failed to delete subject';
            console.error('Error deleting subject:', err);
        } finally {
            deleteLoading = false;
        }
    }

    function cancelDelete() {
        subjectToDelete = null;
    }

    function handleRefresh() {
        loadSubjects();
        loadStatistics();
    }
</script>

<AppLayout {breadcrumbs}>
    <div class="space-y-6">
        <!-- Header -->
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold tracking-tight">Subject Management</h1>
                <p class="text-muted-foreground">
                    Organize your prompts with hierarchical subjects
                </p>
            </div>
            <div class="flex items-center gap-2">
                <Button variant="outline" onclick={handleRefresh} disabled={loading}>
                    <RefreshCw class={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    Refresh
                </Button>
                <Button onclick={handleCreateSubject}>
                    <Plus class="h-4 w-4 mr-2" />
                    New Subject
                </Button>
            </div>
        </div>

        <!-- Error Alert -->
        {#if error}
            <Alert variant="destructive">
                <AlertCircle class="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        {/if}

        <!-- View Tabs -->
        <Tabs value={currentView} onValueChange={handleViewChange}>
            <div class="flex items-center justify-between">
                <TabsList>
                    <TabsTrigger value="tree" class="flex items-center gap-2">
                        <TreePine class="h-4 w-4" />
                        Tree View
                    </TabsTrigger>
                    <TabsTrigger value="grid" class="flex items-center gap-2">
                        <Grid class="h-4 w-4" />
                        Grid View
                    </TabsTrigger>
                    <TabsTrigger value="statistics" class="flex items-center gap-2">
                        <ChartBar class="h-4 w-4" />
                        Statistics
                    </TabsTrigger>
                </TabsList>

                <!-- Filters (only show for grid view) -->
                {#if currentView === 'grid'}
                    <div class="flex items-center gap-2">
                        <div class="relative">
                            <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                placeholder="Search subjects..."
                                bind:value={filters.search}
                                onkeydown={(e) => e.key === 'Enter' && handleSearch()}
                                class="pl-8 w-64"
                            />
                        </div>
                        <Select type="single" onValueChange={handleSortChange}>
                            <SelectTrigger class="w-48">
                                <span>Sort by...</span>
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                                <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                                <SelectItem value="created_at-desc">Newest First</SelectItem>
                                <SelectItem value="created_at-asc">Oldest First</SelectItem>
                                <SelectItem value="prompts_count-desc">Most Prompts</SelectItem>
                                <SelectItem value="children_count-desc">Most Children</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                {/if}
            </div>

            <!-- Tree View -->
            <TabsContent value="tree" class="space-y-4">
                {#if loading}
                    <div class="flex items-center justify-center py-12">
                        <div class="text-center">
                            <div class="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-4"></div>
                            <p class="text-muted-foreground">Loading subjects...</p>
                        </div>
                    </div>
                {:else if subjects.length === 0}
                    <Card>
                        <CardContent class="flex flex-col items-center justify-center py-12">
                            <TreePine class="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 class="text-lg font-semibold mb-2">No subjects found</h3>
                            <p class="text-muted-foreground text-center mb-4">
                                Get started by creating your first subject to organize your prompts.
                            </p>
                            <Button onclick={handleCreateSubject}>
                                <Plus class="h-4 w-4 mr-2" />
                                Create First Subject
                            </Button>
                        </CardContent>
                    </Card>
                {:else}
                    <SubjectTree
                        {subjects}
                        {selectedSubject}
                        onSelect={handleSubjectSelect}
                        onEdit={handleSubjectEdit}
                        onDelete={handleSubjectDelete}
                        onAddChild={handleAddChild}
                        showActions={true}
                        showCounts={true}
                        expandAll={false}
                    />
                {/if}
            </TabsContent>

            <!-- Grid View -->
            <TabsContent value="grid" class="space-y-4">
                {#if loading}
                    <div class="flex items-center justify-center py-12">
                        <div class="text-center">
                            <div class="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-4"></div>
                            <p class="text-muted-foreground">Loading subjects...</p>
                        </div>
                    </div>
                {:else if subjects.length === 0}
                    <Card>
                        <CardContent class="flex flex-col items-center justify-center py-12">
                            <Grid class="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 class="text-lg font-semibold mb-2">No subjects found</h3>
                            <p class="text-muted-foreground text-center mb-4">
                                {filters.search ? 'No subjects match your search criteria.' : 'Get started by creating your first subject.'}
                            </p>
                            {#if !filters.search}
                                <Button onclick={handleCreateSubject}>
                                    <Plus class="h-4 w-4 mr-2" />
                                    Create First Subject
                                </Button>
                            {/if}
                        </CardContent>
                    </Card>
                {:else}
                    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {#each subjects as subject (subject.uuid)}
                            <SubjectCard
                                {subject}
                                onView={handleSubjectSelect}
                                onEdit={handleSubjectEdit}
                                onDelete={handleSubjectDelete}
                                onAddChild={handleAddChild}
                                showActions={true}
                                showPath={true}
                                showStats={true}
                                compact={false}
                            />
                        {/each}
                    </div>
                {/if}
            </TabsContent>

            <!-- Statistics View -->
            <TabsContent value="statistics">
                <SubjectStatistics {statistics} loading={statisticsLoading} />
            </TabsContent>
        </Tabs>
    </div>

    <!-- Delete Confirmation Dialog -->
    <DeleteSubjectDialog
        open={!!subjectToDelete}
        subject={subjectToDelete}
        onConfirm={confirmDelete}
        onCancel={cancelDelete}
        loading={deleteLoading}
    />
</AppLayout>

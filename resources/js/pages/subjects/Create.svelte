<script lang="ts">
    import AppLayout from '@/layouts/AppLayout.svelte';
    import { Alert, AlertDescription } from '@/components/ui/alert';
    import SubjectForm from '@/components/subjects/SubjectForm.svelte';
    import { AlertCircle } from 'lucide-svelte';
    import { api } from '@/lib/api';
    import { router } from '@inertiajs/svelte';
    import type { Subject, CreateSubjectRequest, BreadcrumbItem } from '@/types';
    import { onMount } from 'svelte';

    // State
    let availableParents: Subject[] = $state([]);
    let loading = $state(false);
    let error = $state<string | null>(null);
    let errors = $state<Record<string, string[]>>({});
    let parentUuid = $state<string | null>(null);

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Dashboard', href: '/dashboard' },
        { title: 'Subjects', href: '/subjects' },
        { title: 'Create', href: '/subjects/create' }
    ];

    onMount(() => {
        // Check if parent is specified in URL params
        const urlParams = new URLSearchParams(window.location.search);
        parentUuid = urlParams.get('parent');
        
        loadAvailableParents();
    });

    async function loadAvailableParents() {
        try {
            availableParents = await api.getAvailableParents();
        } catch (err) {
            console.error('Error loading available parents:', err);
            error = 'Failed to load available parent subjects';
        }
    }

    async function handleSubmit(data: CreateSubjectRequest) {
        try {
            loading = true;
            error = null;
            errors = {};

            // If parentUuid is set from URL params, use it
            if (parentUuid && !data.parent_id) {
                data.parent_id = parentUuid;
            }

            const subject = await api.createSubject(data);
            
            // Redirect to the new subject's detail page
            router.visit(`/subjects/${subject.uuid}`, {
                onSuccess: () => {
                    // Success message will be handled by the show page
                }
            });
        } catch (err) {
            loading = false;
            
            if (err instanceof Error) {
                try {
                    // Try to parse validation errors
                    const errorData = JSON.parse(err.message);
                    if (errorData.errors) {
                        errors = errorData.errors;
                    } else {
                        error = errorData.message || 'Failed to create subject';
                    }
                } catch {
                    error = err.message;
                }
            } else {
                error = 'Failed to create subject';
            }
            
            console.error('Error creating subject:', err);
        }
    }

    function handleCancel() {
        router.visit('/subjects');
    }

    // Find the parent subject if specified
    let preselectedParent = $derived(() => {
        if (!parentUuid || !availableParents.length) return null;
        return availableParents.find(p => p.uuid === parentUuid) || null;
    });
</script>

<AppLayout {breadcrumbs}>
    <div class="max-w-4xl mx-auto space-y-6">
        <!-- Header -->
        <div>
            <h1 class="text-3xl font-bold tracking-tight">Create New Subject</h1>
            <p class="text-muted-foreground">
                Create a new subject to organize your prompts hierarchically
            </p>
        </div>

        <!-- General Error Alert -->
        {#if error}
            <Alert variant="destructive">
                <AlertCircle class="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
            </Alert>
        {/if}

        <!-- Parent Context Info -->
        {#if preselectedParent}
            <Alert>
                <AlertCircle class="h-4 w-4" />
                <AlertDescription>
                    This subject will be created as a child of <strong>{preselectedParent()?.name}</strong>
                    {#if preselectedParent()?.full_path}
                        <br />
                        <span class="text-xs text-muted-foreground">Path: {preselectedParent()?.full_path}</span>
                    {/if}
                </AlertDescription>
            </Alert>
        {/if}

        <!-- Subject Form -->
        <div class="flex justify-center">
            <SubjectForm
                subject={null}
                {availableParents}
                onSubmit={handleSubmit}
                onCancel={handleCancel}
                {loading}
                {errors}
                mode="create"
            />
        </div>

        <!-- Help Section -->
        <div class="max-w-2xl mx-auto">
            <div class="bg-muted/50 rounded-lg p-6">
                <h3 class="font-semibold mb-3">Tips for Creating Subjects</h3>
                <ul class="space-y-2 text-sm text-muted-foreground">
                    <li class="flex items-start gap-2">
                        <span class="text-primary">•</span>
                        <span>Use descriptive names that clearly identify the subject area</span>
                    </li>
                    <li class="flex items-start gap-2">
                        <span class="text-primary">•</span>
                        <span>Add descriptions to provide context for other users</span>
                    </li>
                    <li class="flex items-start gap-2">
                        <span class="text-primary">•</span>
                        <span>Create a hierarchical structure by selecting parent subjects</span>
                    </li>
                    <li class="flex items-start gap-2">
                        <span class="text-primary">•</span>
                        <span>Root subjects (without parents) represent main categories</span>
                    </li>
                    <li class="flex items-start gap-2">
                        <span class="text-primary">•</span>
                        <span>You can always reorganize subjects later by editing them</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</AppLayout>
